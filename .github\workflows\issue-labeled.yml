name: Issue Labeled

on:
  issues:
    types: [labeled]

jobs:
  reply-labeled:
    runs-on: ubuntu-latest
    steps:
      - name: remove pending
        if: github.event.label.name == 'enhancement' || github.event.label.name == 'bug'
        uses: actions-cool/issues-helper@v2.1.1
        with:
          actions: 'remove-labels'
          token: ${{ secrets.OPER_TOKEN }}
          issue-number: ${{ github.event.issue.number }}
          labels: 'bug: pending triage'

      - name: need reproduction
        if: github.event.label.name == 'need reproduction'
        uses: actions-cool/issues-helper@v2.1.1
        with:
          actions: 'create-comment, remove-labels'
          token: ${{ secrets.OPER_TOKEN }}
          issue-number: ${{ github.event.issue.number }}
          body: |
            Hello @${{ github.event.issue.user.login }}. Please provide the complete reproduction steps and code. Issues labeled by `need reproduction` will be closed if no activities in 3 days.
          labels: 'bug: pending triage'
