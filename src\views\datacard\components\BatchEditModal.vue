<template>
  <Modal
    v-model:open="visible"
    title="批改设置"
    :width="600"
    :mask-closable="false"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="batch-edit-content">
      <!-- 字段选择 -->
      <div class="field-selection">
        <div class="label">修改字段:</div>
        <Select
          v-model:value="selectedField"
          placeholder="请选择要修改的字段"
          style="width: 100%"
          @change="handleFieldChange"
        >
          <SelectOption
            v-for="header in editableHeaders"
            :key="header.fdname"
            :value="header.fdname"
          >
            {{ header.chnname }}
          </SelectOption>
        </Select>
      </div>

      <!-- 字段信息显示 -->
      <div v-if="selectedFieldInfo" class="field-info">
        <div class="info-item">
          <span class="info-label">字段类型:</span>
          <span>{{ getFieldTypeText(selectedFieldInfo.fdtype) }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">字段长度:</span>
          <span>{{ selectedFieldInfo.fdsize }}</span>
        </div>
        <div class="info-item" v-if="selectedFieldInfo.fddec > 0">
          <span class="info-label">小数位数:</span>
          <span>{{ selectedFieldInfo.fddec }}</span>
        </div>
      </div>

      <!-- 值设置 -->
      <div v-if="selectedField" class="value-setting">
        <div class="label">新值:</div>

        <!-- 根据字段类型显示不同的输入控件 -->
        <!-- 下拉选择 -->
        <Select
          v-if="selectedFieldInfo?.dropdownoptions && selectedFieldInfo.dropdownoptions.length > 0"
          v-model:value="newValue"
          placeholder="请选择新值"
          style="width: 100%"
          allow-clear
        >
          <SelectOption
            v-for="option in selectedFieldInfo.dropdownoptions"
            :key="option.name"
            :value="option.name"
          >
            {{ option.name }}
          </SelectOption>
        </Select>

        <!-- 单选按钮 -->
        <Radio.Group v-else-if="selectedFieldInfo?.isradiobutton" v-model:value="newValue">
          <Radio value="是">是</Radio>
          <Radio value="否">否</Radio>
        </Radio.Group>

        <!-- 多选框 -->
        <Checkbox.Group v-else-if="selectedFieldInfo?.ismultichoice" v-model:value="newValue">
          <Checkbox value="选项1">选项1</Checkbox>
          <Checkbox value="选项2">选项2</Checkbox>
        </Checkbox.Group>

        <!-- 日期选择器 -->
        <DatePicker
          v-else-if="selectedFieldInfo?.isriqi"
          v-model:value="newValue"
          style="width: 100%"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />

        <!-- 普通文本输入 -->
        <Input
          v-else
          v-model:value="newValue"
          :placeholder="`请输入新的${selectedFieldInfo?.chnname || '值'}`"
          :maxlength="selectedFieldInfo?.fdsize"
        />
      </div>

      <!-- 预览区域 -->
      <div v-if="selectedField && newValue !== undefined && newValue !== ''" class="preview-area">
        <div class="label">修改预览:</div>
        <div class="preview-content">
          将把选中的 <span class="highlight">{{ selectedRows.length }}</span> 行数据的
          <span class="highlight">{{ selectedFieldInfo?.chnname }}</span> 字段 修改为:
          <span class="highlight">{{ newValue }}</span>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { Modal, Select, SelectOption, Input, Radio, Checkbox, DatePicker } from 'ant-design-vue';

  interface TableHeader {
    fdname: string;
    chnname: string;
    fdtype: string;
    fdsize: number;
    fddec: number;
    displayorder: string;
    dropdownoptions: any;
    customtag: string | null;
    isnull: boolean;
    isdsp: boolean;
    isriqi?: boolean;
    isthumbnail?: boolean;
    isradiobutton?: boolean;
    ismultichoice?: boolean;
    colwidth: number;
  }

  interface Props {
    visible: boolean;
    selectedRows: any[];
    tableHeaders: TableHeader[];
  }

  const props = withDefaults(defineProps<Props>(), {
    visible: false,
    selectedRows: () => [],
    tableHeaders: () => [],
  });

  const emit = defineEmits<{
    'update:visible': [value: boolean];
    confirm: [fieldName: string, newValue: any, selectedRows: any[]];
  }>();

  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const selectedField = ref('');
  const newValue = ref<any>('');

  // 过滤出可编辑的字段（排除一些系统字段）
  const editableHeaders = computed(() => {
    return props.tableHeaders.filter(
      (header) =>
        !header.isthumbnail && // 排除缩略图字段
        header.isdsp && // 只显示可显示的字段
        !['_X_ROW_KEY'].includes(header.fdname), // 排除系统字段
    );
  });

  // 获取选中字段的详细信息
  const selectedFieldInfo = computed(() => {
    if (!selectedField.value) return null;
    return editableHeaders.value.find((header) => header.fdname === selectedField.value);
  });

  // 监听弹窗关闭，重置数据
  watch(visible, (newVal) => {
    if (!newVal) {
      resetForm();
    }
  });

  function resetForm() {
    selectedField.value = '';
    newValue.value = '';
  }

  function handleFieldChange() {
    // 字段改变时重置新值
    newValue.value = '';
  }

  function getFieldTypeText(fdtype: string): string {
    const typeMap: Record<string, string> = {
      C: '字符型',
      N: '数值型',
      D: '日期型',
      L: '逻辑型',
      M: '备注型',
    };
    return typeMap[fdtype] || fdtype;
  }

  function handleOk() {
    if (!selectedField.value) {
      // 这里可以添加消息提示
      return;
    }

    if (newValue.value === undefined || newValue.value === '') {
      // 这里可以添加消息提示
      return;
    }

    emit('confirm', selectedField.value, newValue.value, props.selectedRows);
    visible.value = false;
  }

  function handleCancel() {
    visible.value = false;
  }
</script>

<style scoped lang="less">
  .batch-edit-content {
    .selected-info {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      padding: 12px;
      border: 1px solid #e0f2fe;
      border-radius: 6px;
      background-color: #f0f9ff;
      font-size: 14px;
    }

    .field-selection,
    .value-setting {
      margin-bottom: 20px;

      .label {
        margin-bottom: 8px;
        color: #333;
        font-weight: 500;
      }
    }

    .field-info {
      margin-bottom: 20px;
      padding: 12px;
      border-radius: 6px;
      background-color: #fafafa;

      .info-item {
        display: flex;
        margin-bottom: 4px;
        font-size: 13px;

        .info-label {
          width: 80px;
          color: #666;
        }
      }
    }

    .preview-area {
      .label {
        margin-bottom: 8px;
        color: #333;
        font-weight: 500;
      }

      .preview-content {
        padding: 12px;
        border: 1px solid #ffd666;
        border-radius: 6px;
        background-color: #fff7e6;
        font-size: 14px;

        .highlight {
          color: #d46b08;
          font-weight: 500;
        }
      }
    }
  }
</style>
