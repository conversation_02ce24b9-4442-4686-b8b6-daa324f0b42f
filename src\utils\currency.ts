import nzhcn from 'nzh/cn';

/**
 * 金额格式化工具类
 */
export class CurrencyFormatter {
  /**
   * 格式化数字金额，保持指定的小数位数
   * @param amount 金额数值
   * @param decimals 小数位数，默认2位
   * @returns 格式化后的数字字符串
   */
  static formatNumber(amount: number | string, decimals: number = 2): string {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (isNaN(num)) return '0.00';

    return num.toFixed(decimals);
  }

  /**
   * 转换为中文大写金额，保持与数字金额相同的小数位数
   * @param amount 金额数值
   * @param decimals 小数位数，默认2位
   * @returns 中文大写金额字符串
   */
  static toChineseUppercase(amount: number | string, decimals: number = 2): string {
    try {
      const num = typeof amount === 'string' ? parseFloat(amount) : amount;
      if (isNaN(num)) return '零元整';

      // 先格式化为指定小数位数
      const formattedNum = parseFloat(num.toFixed(decimals));

      // 使用nzh库转换为中文大写
      const chineseAmount = nzhcn.encodeB(formattedNum);

      return chineseAmount;
    } catch (error) {
      console.error('转换中文大写金额失败:', error);
      return '零元整';
    }
  }

  /**
   * 从中文大写金额转换为数字金额
   * @param chineseAmount 中文大写金额
   * @param decimals 保持的小数位数
   * @returns 数字金额
   */
  static fromChineseUppercase(chineseAmount: string, decimals: number = 2): number {
    try {
      if (!chineseAmount || typeof chineseAmount !== 'string') return 0;

      // 使用nzh库解码中文数字
      const num = nzhcn.decodeB(chineseAmount);

      if (typeof num === 'number' && !isNaN(num)) {
        // 保持指定的小数位数
        return parseFloat((num as number).toFixed(decimals));
      }

      return 0;
    } catch (error) {
      console.error('从中文大写金额转换失败:', error);
      return 0;
    }
  }

  /**
   * 同时格式化数字金额和中文大写金额，确保精度一致
   * @param amount 金额数值
   * @param decimals 小数位数
   * @returns 包含数字金额和中文大写金额的对象
   */
  static formatBoth(
    amount: number | string,
    decimals: number = 2,
  ): {
    number: string;
    chinese: string;
    value: number;
  } {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    const formattedValue = isNaN(num) ? 0 : parseFloat(num.toFixed(decimals));

    return {
      number: this.formatNumber(formattedValue, decimals),
      chinese: this.toChineseUppercase(formattedValue, decimals),
      value: formattedValue,
    };
  }
}

/**
 * 快捷函数：格式化金额为数字字符串
 */
export const formatCurrency = (amount: number | string, decimals: number = 2): string => {
  return CurrencyFormatter.formatNumber(amount, decimals);
};

/**
 * 快捷函数：转换为中文大写金额
 */
export const toChinese = (amount: number | string, decimals: number = 2): string => {
  return CurrencyFormatter.toChineseUppercase(amount, decimals);
};

/**
 * 快捷函数：从中文大写金额转换为数字
 */
export const fromChinese = (chineseAmount: string, decimals: number = 2): number => {
  return CurrencyFormatter.fromChineseUppercase(chineseAmount, decimals);
};
