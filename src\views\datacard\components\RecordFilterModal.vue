<template>
  <Modal
    v-model:open="modalVisible"
    title="记录过滤"
    :width="800"
    @ok="handleOk"
    @cancel="handleCancel"
    okText="确定"
    cancelText="取消"
  >
    <div class="filter-form">
      <Form :model="filterData" layout="vertical">
        <Row :gutter="16">
          <Col
            v-for="header in tableHeaders || []"
            :key="header?.fdname || Math.random()"
            v-show="header && header.fdname && header.chnname"
            :span="12"
            class="mb-4"
          >
            <FormItem :label="header.chnname" :name="header.fdname">
              <div class="filter-item">
                <Select
                  :value="filterData[header.fdname]?.matchType"
                  @update:value="(val) => updateFilterField(header.fdname, 'matchType', val)"
                  placeholder="匹配类型"
                  class="match-type-select"
                  style="width: 120px; margin-right: 8px"
                >
                  <SelectOption value="EXACT">精准匹配</SelectOption>
                  <SelectOption value="FUZZY">模糊匹配</SelectOption>
                </Select>
                <Input
                  :value="filterData[header.fdname]?.value"
                  @update:value="(val) => updateFilterField(header.fdname, 'value', val)"
                  :placeholder="`请输入${header.chnname}`"
                  style="flex: 1"
                />
              </div>
            </FormItem>
          </Col>
        </Row>
      </Form>
    </div>
  </Modal>
</template>

<script setup lang="ts">
  import { ref, reactive, watch } from 'vue';
  import { Modal, Form, FormItem, Input, Select, SelectOption, Row, Col } from 'ant-design-vue';

  interface Props {
    visible: boolean;
    tableHeaders: Array<{
      fdname: string;
      chnname: string;
      fdtype: string;
      fdsize: number;
      fddec: number;
      displayorder: string;
      dropdownoptions: any;
      customtag: string | null;
      isnull: boolean;
      isdsp: boolean;
      isriqi?: boolean;
      isthumbnail?: boolean;
      isradiobutton?: boolean;
      ismultichoice?: boolean;
      colwidth: number;
    }>;
    currentFilters?: Record<string, { value: any; matchType: string }>;
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void;
    (e: 'confirm', filters: Record<string, { value: any; matchType: string }>): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    visible: false,
    tableHeaders: () => [],
    currentFilters: () => ({}),
  });

  const emit = defineEmits<Emits>();

  const modalVisible = ref(false);
  const filterData = reactive<Record<string, { value: any; matchType: string }>>({});

  // 更新过滤字段值的函数
  const updateFilterField = (fieldName: string, fieldType: 'value' | 'matchType', value: any) => {
    if (!filterData[fieldName]) {
      filterData[fieldName] = { value: '', matchType: 'FUZZY' };
    }
    filterData[fieldName][fieldType] = value;
  };

  // 监听 visible 变化
  watch(
    () => props.visible,
    (newVal) => {
      modalVisible.value = newVal;
      if (newVal) {
        // 添加数据验证，确保tableHeaders有效
        if (!props.tableHeaders || props.tableHeaders.length === 0) {
          console.warn('RecordFilterModal: tableHeaders为空，关闭模态框');
          emit('update:visible', false);
          return;
        }
        initFilterData();
      }
    },
    { immediate: true },
  );

  // 监听内部 visible 变化
  watch(modalVisible, (newVal) => {
    emit('update:visible', newVal);
  });

  // 初始化过滤数据
  const initFilterData = () => {
    // 清空之前的数据
    Object.keys(filterData).forEach((key) => {
      delete filterData[key];
    });

    // 为每个字段初始化过滤条件
    if (props.tableHeaders && props.tableHeaders.length > 0) {
      props.tableHeaders.forEach((header) => {
        // 添加字段验证，确保fdname存在
        if (header && header.fdname) {
          filterData[header.fdname] = {
            value: props.currentFilters[header.fdname]?.value || '',
            matchType: props.currentFilters[header.fdname]?.matchType || 'FUZZY',
          };
        }
      });
    }
  };

  const handleOk = () => {
    // 过滤掉值为空的条件
    const filters: Record<string, { value: any; matchType: string }> = {};
    Object.keys(filterData).forEach((key) => {
      if (filterData[key].value && String(filterData[key].value).trim() !== '') {
        filters[key] = {
          value: filterData[key].value,
          matchType: filterData[key].matchType,
        };
      }
    });

    emit('confirm', filters);
    modalVisible.value = false;
  };

  const handleCancel = () => {
    modalVisible.value = false;
  };
</script>

<style scoped lang="less">
  .filter-form {
    max-height: 500px;
    overflow-y: auto;
  }

  .filter-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .match-type-select {
    flex-shrink: 0;
  }

  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }

  :deep(.ant-form-item-label) {
    padding-bottom: 4px;
  }
</style>
