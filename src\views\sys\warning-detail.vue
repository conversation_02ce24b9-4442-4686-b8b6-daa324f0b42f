<template>
  <div>
    <a-page-header :title="pageTitle" @back="handleBack" class="mb-4">
      <template #extra>
        <a-button @click="handleRefresh" :loading="loading">刷新</a-button>
      </template>
    </a-page-header>

    <a-card>
      <a-table
        :dataSource="warningDetailList"
        :columns="columns"
        :loading="loading"
        :pagination="false"
        :scroll="{ x: 1200 }"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'kcsl'">
            <span :class="{ 'text-red-500': record.kcsl <= 0 }">
              {{ record.kcsl }}
            </span>
          </template>
          <template v-else-if="column.dataIndex === 'sxrq'">
            <span :class="{ 'text-red-500': isExpiringSoon(record.sxrq) }">
              {{ record.sxrq }}
            </span>
          </template>
          <template v-else-if="column.dataIndex === 'dqts'">
            <span :class="getDqtsClass(record.dqts)"> {{ record.dqts }} 天 </span>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, computed } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { message, Card as ACard, Table as ATable } from 'ant-design-vue';
  import { getWarningDetail, type WarningDetailVo } from '@/api/system';
  import { useTabs } from '@/hooks/web/useTabs';

  const route = useRoute();
  const router = useRouter();
  const { setTitle } = useTabs();

  const loading = ref(false);
  const warningDetailList = ref<WarningDetailVo[]>([]);
  const pageTitle = ref('系统预警详情');
  const tableHeader = ref<{ [key: string]: string }>({});

  // 默认表格列定义（用于向后兼容）
  const defaultColumns = [
    {
      title: '产品编号',
      dataIndex: 'dspcode',
      key: 'dspcode',
      width: 120,
      fixed: 'left',
    },
    {
      title: '产品名称',
      dataIndex: 'dspname',
      key: 'dspname',
      width: 200,
      ellipsis: true,
    },
    {
      title: '生产厂家',
      dataIndex: 'shengccj',
      key: 'shengccj',
      width: 200,
      ellipsis: true,
    },
    {
      title: '规格/型号',
      dataIndex: 'dspspgg',
      key: 'dspspgg',
      width: 150,
      ellipsis: true,
    },
    {
      title: '计量单位',
      dataIndex: 'jldw',
      key: 'jldw',
      width: 80,
    },
    {
      title: '库存数量',
      dataIndex: 'kcsl',
      key: 'kcsl',
      width: 100,
      align: 'right',
    },
    {
      title: '批号',
      dataIndex: 'miejph',
      key: 'miejph',
      width: 120,
    },
    {
      title: '有效期至',
      dataIndex: 'sxrq',
      key: 'sxrq',
      width: 100,
    },
    {
      title: '批次',
      dataIndex: 'picih',
      key: 'picih',
      width: 150,
      ellipsis: true,
    },
    {
      title: '到期天数',
      dataIndex: 'dqts',
      key: 'dqts',
      width: 100,
      align: 'right',
    },
  ];

  // 动态表格列定义
  const columns = computed(() => {
    // 如果有表头映射数据，使用动态列
    if (Object.keys(tableHeader.value).length > 0) {
      return Object.entries(tableHeader.value).map(([field, title]) => ({
        title,
        dataIndex: field,
        key: field,
        width: getColumnWidth(field),
        align: getColumnAlign(field) as 'left' | 'right' | 'center',
        ellipsis: shouldEllipsis(field),
        fixed: field === 'dspcode' ? ('left' as const) : undefined,
      }));
    }
    // 否则使用默认列定义
    return defaultColumns;
  });

  // 获取列宽度
  const getColumnWidth = (field: string) => {
    const widthMap: { [key: string]: number } = {
      dspcode: 120,
      dspname: 200,
      shengccj: 200,
      dspspgg: 150,
      jldw: 80,
      kcsl: 100,
      miejph: 120,
      sxrq: 100,
      picih: 150,
      dqts: 100,
    };
    return widthMap[field] || 120;
  };

  // 获取列对齐方式
  const getColumnAlign = (field: string) => {
    const alignMap: { [key: string]: string } = {
      kcsl: 'right',
      dqts: 'right',
    };
    return alignMap[field] || 'left';
  };

  // 判断是否需要省略号
  const shouldEllipsis = (field: string) => {
    const ellipsisFields = ['dspname', 'shengccj', 'dspspgg', 'picih'];
    return ellipsisFields.includes(field);
  };

  // 判断是否即将到期
  const isExpiringSoon = (sxrq: string) => {
    if (!sxrq) return false;
    const expireDate = new Date(sxrq);
    const today = new Date();
    const diffTime = expireDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 30; // 30天内到期显示红色
  };

  // 获取到期天数样式类
  const getDqtsClass = (dqts: number) => {
    if (dqts <= 0) return 'text-red-600 font-semibold';
    if (dqts <= 7) return 'text-red-500';
    if (dqts <= 30) return 'text-orange-500';
    return 'text-green-500';
  };

  // 获取预警详情数据
  const fetchWarningDetail = async () => {
    const gnbh = route.query.gnbh as string;
    if (!gnbh) {
      message.error('缺少功能编号参数');
      return;
    }

    // 设置页面标题和标签页标题，包含功能编号
    const titleWithGnbh = `系统预警详情 - ${gnbh}`;
    pageTitle.value = titleWithGnbh;
    setTitle(titleWithGnbh);

    loading.value = true;
    try {
      const apiResponse = await getWarningDetail(gnbh);

      // 处理API响应格式
      if (apiResponse && typeof apiResponse === 'object') {
        // 提取data部分
        const responseData = apiResponse;

        if (responseData && typeof responseData === 'object') {
          // 检查是否为新格式（包含 tableheader 和 tablevalue）
          if ('tableheader' in responseData && 'tablevalue' in responseData) {
            // 新格式：从 data 对象中提取数据
            const { tableheader, tablevalue } = responseData;

            // 更新表头映射
            if (tableheader && typeof tableheader === 'object') {
              tableHeader.value = tableheader;
            } else {
              tableHeader.value = {};
            }

            // 更新表格数据
            if (Array.isArray(tablevalue)) {
              warningDetailList.value = tablevalue;
            } else {
              warningDetailList.value = [];
              console.warn('tablevalue 不是数组格式:', tablevalue);
            }
          } else if (Array.isArray(responseData)) {
            // 旧格式：直接是数组（向后兼容）
            warningDetailList.value = responseData;
            tableHeader.value = {}; // 清空表头映射，使用默认列
          } else {
            // 未知格式
            console.warn('未知的响应数据格式:', responseData);
            warningDetailList.value = [];
            tableHeader.value = {};
          }
        } else {
          // 数据部分为空或无效
          warningDetailList.value = [];
          tableHeader.value = {};
        }
      } else {
        // 响应为空或无效
        warningDetailList.value = [];
        tableHeader.value = {};
      }
    } catch (error) {
      console.error('获取预警详情失败:', error);
      message.error('获取预警详情失败，请稍后重试');
      // 重置数据
      warningDetailList.value = [];
      tableHeader.value = {};
    } finally {
      loading.value = false;
    }
  };

  // 返回上一页
  const handleBack = () => {
    router.back();
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchWarningDetail();
  };

  onMounted(() => {
    fetchWarningDetail();
  });
</script>

<style lang="scss" scoped>
  :deep(.ant-table-cell) {
    .text-red-500 {
      color: #ef4444;
    }

    .text-red-600 {
      color: #dc2626;
    }

    .text-orange-500 {
      color: #f59e0b;
    }

    .text-green-500 {
      color: #10b981;
    }
  }

  :deep(.ant-card-body) {
    padding: 0;
  }
</style>
