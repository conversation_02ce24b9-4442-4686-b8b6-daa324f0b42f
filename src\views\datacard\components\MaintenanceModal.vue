<template>
  <BasicModal
    v-bind="$attrs"
    :open="visible"
    :title="title"
    @ok="handleSave"
    @cancel="handleCancel"
    width="800px"
  >
    <div class="p-2">
      <div class="flex mb-2">
        <a-button type="link" preIcon="ant-design:plus-circle-outlined" @click="handleAdd">
          新增
        </a-button>
        <a-button
          type="link"
          preIcon="ant-design:delete-outlined"
          :disabled="!hasSelected"
          @click="handleDelete"
        >
          删除
        </a-button>
        <a-button type="link" preIcon="ant-design:save-outlined" @click="handleSave">
          保存
        </a-button>
        <a-button type="link" preIcon="ant-design:close-outlined" @click="handleCancel">
          关闭
        </a-button>
      </div>

      <!-- 分割线 -->
      <div class="horizontal-line"></div>

      <!-- 单位信息显示区域 -->
      <div class="unit-info-container mb-3 w-80%">
        <div class="grid grid-cols-2 gap-x-4">
          <FormItem label="单位编号" class="mb-2">
            <Input :value="props.selectedRow?.ddwcode || ''" disabled />
          </FormItem>
          <FormItem label="单位名称" class="mb-2">
            <Input :value="props.selectedRow?.ddwname || ''" disabled />
          </FormItem>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <VxeBasicTable ref="tableRef" v-bind="gridOptions" v-on="gridEvents" :loading="loading">
          <template #empty>
            <p>没有更多数据了！</p>
          </template>
        </VxeBasicTable>
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <Modal
      v-model:visible="editModalVisible"
      :title="editMode === 'add' ? '新增数据' : '编辑数据'"
      @ok="handleEditOk"
      @cancel="handleEditCancel"
      :destroyOnClose="true"
      :maskClosable="false"
      :width="700"
    >
      <Spin :spinning="editModalLoading">
        <Form :model="editForm" layout="horizontal">
          <div class="grid grid-cols-2 gap-x-4 max-h-[500px] overflow-y-auto p-2">
            <template v-for="(col, index) in columns" :key="index">
              <FormItem
                :label="col.title"
                :name="col.field"
                v-if="col.field"
                :class="{ 'col-span-2': isFullWidthField(col.field) }"
              >
                <!-- 根据字段类型渲染不同的输入控件 -->
                <!-- 日期类型 -->
                <DatePicker
                  v-if="isDateField(col)"
                  v-model:value="editForm[col.field]"
                  style="width: 100%"
                  :valueFormat="'YYYY-MM-DD'"
                  :format="'YYYY-MM-DD'"
                  :placeholder="`请选择${col.title}`"
                />
                <!-- 整数类型 -->
                <InputNumber
                  v-else-if="isIntegerField(col)"
                  v-model:value="editForm[col.field]"
                  style="width: 100%"
                  :precision="0"
                  :step="1"
                  :placeholder="`请输入${col.title}`"
                />
                <!-- 实数类型 -->
                <InputNumber
                  v-else-if="isDecimalField(col)"
                  v-model:value="editForm[col.field]"
                  style="width: 100%"
                  :precision="col.fddec || 2"
                  :step="0.01"
                  :placeholder="`请输入${col.title}`"
                />
                <!-- 默认文本类型 -->
                <Input
                  v-else
                  v-model:value="editForm[col.field]"
                  :placeholder="`请输入${col.title}`"
                  :maxLength="getFieldMaxLength(col.field)"
                />
              </FormItem>
            </template>
          </div>
        </Form>
      </Spin>
    </Modal>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, watch, reactive, nextTick } from 'vue';
  import {
    Modal,
    message,
    Spin,
    Form,
    FormItem,
    Input,
    DatePicker,
    InputNumber,
  } from 'ant-design-vue';
  import { getSonWhApi, editSonWhApi } from '@/api/datacard/index';
  import { VxeBasicTable, VxeGridInstance, VxeGridListeners } from '@/components/VxeTable';
  import dayjs from 'dayjs';
  import { BasicModal } from '@/components/Modal';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    ddwid: {
      type: String,
      required: true,
    },
    whtype: {
      type: String,
      required: true,
    },
    selectedRow: {
      type: Object,
      default: () => ({}),
    },
  });

  const emit = defineEmits(['update:visible', 'success']);

  // 内部visible状态
  const visible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val),
  });

  // 根据维护类型设置标题
  const title = computed(() => {
    return props.whtype === 'yhzh' ? '银行账号维护' : '联系人维护';
  });

  // 状态管理
  const loading = ref(false);
  const tableRef = ref<VxeGridInstance>();
  const tableData = ref<any[]>([]);
  const columns = ref<any[]>([]);
  const originalData = ref<any[]>([]);
  const selectedRowKeys = ref<string[]>([]);

  // 编辑弹窗相关
  const editModalVisible = ref(false);
  const editModalLoading = ref(false);
  const editMode = ref<'add' | 'edit'>('add');
  const editForm = reactive<Record<string, any>>({});
  const currentEditRow = ref<any>(null);

  // 字段最大长度映射
  const fieldMaxLengths = ref<Record<string, number>>({});

  // 检查字段类型
  const isDateField = (col: any) => {
    const field = col.field;
    // 优先判断字段名中包含日期相关关键词
    return field.includes('rq') || field.includes('date') || field.includes('time');
  };

  const isNumberField = (col: any) => {
    // 根据fdtype判断是否为数字类型
    return col.fdtype === '实数' || col.fdtype === '整数';
  };

  const isIntegerField = (col: any) => {
    // 判断是否为整数类型
    return col.fdtype === '整数';
  };

  const isDecimalField = (col: any) => {
    // 判断是否为实数类型
    return col.fdtype === '实数';
  };

  // 判断是否需要全宽显示的字段（比如备注、经营范围等）
  const isFullWidthField = (field: string) => {
    const fullWidthFields = [
      'beizhu',
      'bz',
      'jingyfw',
      'qxjingyfw',
      'address',
      'addr',
      'dz',
      'ms',
      'miaoshu',
    ];
    return fullWidthFields.some((item) => field.toLowerCase().includes(item));
  };

  // 获取字段最大长度
  const getFieldMaxLength = (field: string) => {
    return fieldMaxLengths.value[field] || 50;
  };

  // 是否有选择行
  const hasSelected = computed(() => selectedRowKeys.value.length > 0);

  // 当前编辑的行索引
  const currentEditIndex = ref(-1);

  // 表格配置
  const gridOptions = reactive({
    id: 'MaintenanceTable',
    border: true,
    size: 'mini',
    height: '400',
    rowConfig: {
      keyField: '_index',
      isHover: true,
      height: 40,
    },
    columnConfig: {
      resizable: true,
    },
    checkboxConfig: {
      reserve: true,
      highlight: true,
      range: true,
    },
    editConfig: {
      trigger: 'dblclick',
      mode: 'cell',
      showStatus: true,
    },
    toolbarConfig: {
      // 禁用工具栏功能
      enabled: false,
      // 不显示工具栏按钮
      buttons: [],
      // 不显示右侧按钮
      perfect: false,
    },
    columns: [] as any[],
    data: [] as any[],
  });

  // 表格事件处理
  const gridEvents: VxeGridListeners = {
    checkboxChange({ records }) {
      selectedRowKeys.value = records.map((item) => item._index);
    },
    checkboxAll({ records }) {
      selectedRowKeys.value = records.map((item) => item._index);
    },
    editClosed() {
      currentEditIndex.value = -1;
    },
    // 双击行事件，打开编辑弹窗
    cellDblclick({ row }) {
      // 确保行不是空对象
      if (row && Object.keys(row).filter((key) => key !== '_X_ROW_KEY').length > 0) {
        openEditModal('edit', row);
      }
    },
  };

  // 打开编辑弹窗
  const openEditModal = (mode: 'add' | 'edit', row?: any) => {
    editMode.value = mode;

    // 重置表单
    Object.keys(editForm).forEach((key) => {
      delete editForm[key];
    });

    if (mode === 'add') {
      // 新增模式：设置默认值
      columns.value.forEach((col) => {
        if (col.field) {
          if (isDateField(col)) {
            editForm[col.field] = null;
          } else if (isNumberField(col)) {
            editForm[col.field] = 0;
          } else {
            editForm[col.field] = '';
          }
        }
      });
    } else if (mode === 'edit' && row) {
      // 编辑模式：填充现有数据
      currentEditRow.value = row;
      columns.value.forEach((col) => {
        if (col.field) {
          if (isDateField(col) && row[col.field]) {
            editForm[col.field] = row[col.field] ? dayjs(row[col.field]) : null;
          } else {
            editForm[col.field] = row[col.field];
          }
        }
      });
    }

    editModalVisible.value = true;
  };

  // 确认编辑操作
  const handleEditOk = async () => {
    editModalLoading.value = true;
    try {
      // 校验必填字段
      for (const col of columns.value) {
        if (col.required && !editForm[col.field] && editForm[col.field] !== 0) {
          message.error(`${col.title}不能为空`);
          editModalLoading.value = false;
          return;
        }
      }

      // 准备保存数据
      const formData = { ...editForm };

      // 处理日期字段
      columns.value.forEach((col) => {
        if (col.field && isDateField(col) && formData[col.field]) {
          formData[col.field] = dayjs(formData[col.field]).format('YYYY-MM-DD');
        }
      });

      if (editMode.value === 'add') {
        // 添加新行
        formData._index = `new_${Date.now()}`;
        tableData.value = [formData, ...tableData.value];
        gridOptions.data = tableData.value;

        // 确保表格刷新
        nextTick(() => {
          // 重新加载数据到表格
          tableRef.value?.loadData(tableData.value);

          // 滚动到第一行
          tableRef.value?.scrollTo(0, 0);

          // 设置新添加的行为当前行
          tableRef.value?.setCurrentRow(tableData.value[0]);
        });

        message.success('已添加新数据');
      } else {
        // 更新现有行
        const index = tableData.value.findIndex(
          (item) => item._index === currentEditRow.value._index,
        );
        if (index !== -1) {
          // 保持_index不变
          formData._index = currentEditRow.value._index;
          tableData.value[index] = formData;
          gridOptions.data = [...tableData.value];

          // 确保表格刷新
          nextTick(() => {
            // 重新加载数据到表格
            tableRef.value?.loadData(tableData.value);

            // 定位到编辑的行
            const row = tableData.value[index];
            if (row) {
              tableRef.value?.setCurrentRow(row);
              tableRef.value?.scrollToRow(row);
            }
          });

          message.success('数据更新成功');
        }
      }

      // 关闭弹窗
      editModalVisible.value = false;
    } catch (error) {
      console.error('处理数据失败：', error);
      message.error('操作失败');
    } finally {
      editModalLoading.value = false;
    }
  };

  // 取消编辑操作
  const handleEditCancel = () => {
    editModalVisible.value = false;
  };

  // 加载数据
  const loadData = async () => {
    if (!props.ddwid || !props.whtype) {
      message.error('缺少必要参数');
      return;
    }

    loading.value = true;
    try {
      const res = await getSonWhApi({
        ddwid: props.ddwid,
        whtype: props.whtype as 'yhzh' | 'lxr',
      });

      const { tableheader, tablevalue } = res || { tableheader: [], tablevalue: [] };
      // 处理表头，按displayorder排序
      const columnsList: any[] = (tableheader || [])
        .sort((a, b) => a.displayorder - b.displayorder)
        .map((item) => {
          // 使用实际的字段长度
          fieldMaxLengths.value[item.fdname] = item.fdsize;

          return {
            field: item.fdname,
            title: item.chnname,
            width: 120,
            editRender: { name: 'input' },
            required: item.fdname === 'name' || item.fdname === 'code', // 示例：name和code字段为必填
            // 保存完整的字段信息
            fdtype: item.fdtype,
            fdsize: item.fdsize,
            fddec: item.fddec,
            displayorder: item.displayorder,
          };
        });

      // 在列配置前添加复选框列
      const finalColumnsList = [
        {
          type: 'checkbox',
          width: 60,
          fixed: 'left',
        },
        ...columnsList,
      ];

      // 设置表格列
      columns.value = columnsList;
      gridOptions.columns = finalColumnsList;
      // 处理表格数据
      const formattedData = (tablevalue || []).map((item, index) => ({
        ...item,
        _index: `${index}`,
      }));
      tableData.value = formattedData;
      gridOptions.data = formattedData;

      // 保存原始数据用于比较变更
      originalData.value = JSON.parse(JSON.stringify(formattedData));

      // 重置选择
      selectedRowKeys.value = [];

      // 等待表格渲染
      await nextTick();
      // 先刷新列配置
      tableRef.value?.refreshColumn();
      // 重新加载数据，确保空数据也能正确显示
      tableRef.value?.loadData(formattedData);
    } catch (error) {
      console.error('加载数据失败：', error);
      message.error('加载数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 监听visible变化
  watch(
    () => props.visible,
    (val) => {
      if (val) {
        loadData();
      }
    },
    { immediate: true },
  );

  // 添加新行
  const handleAdd = () => {
    // 检查是否已经加载了表头数据
    if (!columns.value || columns.value.length === 0) {
      message.warning('表头数据尚未加载完成，无法添加新行');
      return;
    }

    // 打开新增弹窗
    openEditModal('add');
  };

  // 删除选中的行
  const handleDelete = () => {
    if (selectedRowKeys.value.length === 0) {
      message.warning('请选择要删除的行');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除所选的 ${selectedRowKeys.value.length} 行数据吗？`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk() {
        const updatedData = tableData.value.filter(
          (item) => !selectedRowKeys.value.includes(item._index),
        );
        tableData.value = updatedData;
        gridOptions.data = updatedData;
        selectedRowKeys.value = [];

        // 确保表格刷新并清除选择状态
        nextTick(() => {
          tableRef.value?.loadData(tableData.value);
          tableRef.value?.clearCheckboxRow();
          tableRef.value?.clearCheckboxReserve();
        });
      },
    });
  };

  // 保存数据
  const handleSave = async () => {
    loading.value = true;
    // 准备提交的数据，去掉_index字段
    const submitData = tableData.value.map((item) => {
      const { _index, ...rest } = item;
      return rest;
    });
    try {
      // 调用保存接口
      const res = await editSonWhApi({
        ddwid: props.ddwid,
        whtype: props.whtype as 'yhzh' | 'lxr',
        data: submitData,
      });
      console.log(res);
      emit('success');
    } catch (error) {
      console.error('保存数据失败：', error);
    } finally {
      loading.value = false;
    }
  };

  // 取消操作
  const handleCancel = () => {
    emit('update:visible', false);
  };
</script>

<style scoped lang="less">
  :deep(.ant-form-item) {
    margin-bottom: 5px;
  }

  :deep(.ant-form-item-label) {
    width: 120px;
    padding-right: 8px;
    text-align: right;
  }

  :deep(.ant-input) {
    height: 27px;
  }

  :deep(.ant-btn-link) {
    height: 32px;
    padding: 0 3px;
    transition: all 0.3s;
    color: #595959;
    font-size: 14px;

    &:hover {
      color: rgb(188 78 39);
    }

    .anticon {
      margin-right: 4px;
      font-size: 14px;
    }

    /* 覆盖 antd 的默认间距 */
    &.ant-btn > .anticon + span,
    &.ant-btn > span + .anticon {
      margin-inline-start: 0 !important;
    }
  }

  .horizontal-line {
    height: 1px;
    margin: 1px 0;
    margin-bottom: 12px;
    background-color: #d9d9d9;
  }

  .unit-info-container {
    :deep(.ant-form-item) {
      margin-bottom: 0;
    }

    :deep(.ant-form-item-label) {
      width: 80px;
      padding-right: 8px;
      text-align: right;
    }

    :deep(.ant-input[disabled]) {
      border-color: #d9d9d9;
      background-color: #f5f5f5;
      color: #666;
    }
  }

  .table-container {
    min-height: 400px;
  }
</style>
